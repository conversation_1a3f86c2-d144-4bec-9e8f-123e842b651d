<?php

namespace App\Http\Controllers\API;

use App\Enums\RoleEnum;
use Exception;
use App\Models\Bid;
use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Exceptions\ExceptionHandler;
use App\Helpers\Helpers;
use App\Http\Requests\API\CreateBidRequest;
use App\Http\Requests\API\UpdateBidRequest;
use App\Http\Requests\API\UpdateBidStatusRequest;
use App\Http\Resources\BidResource;
use App\Repositories\API\BidRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BidController extends Controller
{
    public $repository;

    public function __construct(BidRepository $repository)
    {
        $this->authorizeResource(Bid::class, 'bid');
        $this->repository = $repository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $bids = $this->repository->whereNull('deleted_at');
            $bids = $this->filter($bids, $request);
            $bids = $bids->with(['jobBooking', 'jobBooking.user', 'provider'])->latest('created_at')->paginate($request->paginate ?? 15);
            
            return response()->json([
                'success' => true,
                'data' => BidResource::collection($bids->items()),
                'pagination' => [
                    'current_page' => $bids->currentPage(),
                    'last_page' => $bids->lastPage(),
                    'per_page' => $bids->perPage(),
                    'total' => $bids->total(),
                ]
            ]);

        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Bid $bid)
    {
        try {
            $bid->load(['jobBooking', 'jobBooking.user', 'provider']);

            return response()->json([
                'success' => true,
                'data' => new BidResource($bid)
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Bid $bid)
    {
       //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateBidRequest $request)
    {
        try {
            $bid = $this->repository->store($request);
            $bid->load(['jobBooking', 'jobBooking.user', 'provider']);

            return response()->json([
                'success' => true,
                'data' => new BidResource($bid),
                'message' => 'Bid created successfully'
            ], 201);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBidRequest $request, Bid $bid)
    {
        try {
            // Only allow providers to update their own bids
            $user = Auth::guard('api')->user();
            if ($user && $user->hasRole(RoleEnum::PROVIDER) && $bid->provider_id !== Helpers::getCurrentUserId()) {
                throw new Exception('You can only update your own bids', 403);
            }
            
            $result = $this->repository->update($request->all(), $bid->id);
            
            if ($result instanceof \App\Models\Service) {
                return response()->json([
                    'success' => true,
                    'data' => $result,
                    'message' => 'Bid accepted and service created successfully'
                ]);
            }
            
            $result->load(['jobBooking', 'jobBooking.user', 'provider']);

            return response()->json([
                'success' => true,
                'data' => new BidResource($result),
                'message' => 'Bid updated successfully'
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Bid $bid)
    {
        try {
            // Only allow providers to delete their own pending bids
            $user = Auth::guard('api')->user();
            if ($user && $user->hasRole(RoleEnum::PROVIDER) && $bid->provider_id !== Helpers::getCurrentUserId()) {
                throw new Exception('You can only delete your own bids', 403);
            }
            
            if ($bid->status !== 'requested') {
                throw new Exception('You can only delete pending bids', 400);
            }
            
            $bid->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Bid withdrawn successfully'
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function filter($bids, $request)
    {
        $roleName = Helpers::getCurrentRoleName();
        if ($request->field && $request->sort) {
            $bids = $bids->orderBy($request->field, $request->sort);
        }

        if ($request->job_booking_id) {
            $bids = $bids->where('job_booking_id', $request->job_booking_id);
        }

        if ($request->start_date && $request->end_date) {
            $bids = $bids->whereBetween('created_at', [$request->start_date, $request->end_date]);
        }

        if ($roleName == RoleEnum::PROVIDER) {
            $bids = $bids->where('provider_id', Helpers::getCurrentUserId());
        }

        return $bids;
    }

    /**
     * Get provider's own bids (Task 8)
     */
    public function providerBids(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !$user->hasRole(RoleEnum::PROVIDER)) {
                throw new Exception('Only providers can access this endpoint', 403);
            }

            $bids = Bid::whereNull('deleted_at')
                ->where('provider_id', Helpers::getCurrentUserId())
                ->with(['jobBooking', 'jobBooking.user', 'provider']);

            // Apply filters
            if ($request->status) {
                $bids = $bids->where('status', $request->status);
            }

            if ($request->job_booking_id) {
                $bids = $bids->where('job_booking_id', $request->job_booking_id);
            }

            if ($request->start_date && $request->end_date) {
                $bids = $bids->whereBetween('created_at', [$request->start_date, $request->end_date]);
            }

            // Apply sorting
            if ($request->field && $request->sort) {
                $bids = $bids->orderBy($request->field, $request->sort);
            } else {
                $bids = $bids->latest('created_at');
            }

            $bids = $bids->paginate($request->paginate ?? 15);

            return response()->json([
                'success' => true,
                'data' => BidResource::collection($bids->items()),
                'pagination' => [
                    'current_page' => $bids->currentPage(),
                    'last_page' => $bids->lastPage(),
                    'per_page' => $bids->perPage(),
                    'total' => $bids->total(),
                ]
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get bids for a specific service request (Task 11)
     * Note: This endpoint is deprecated as the system has migrated to job bookings.
     * Use job booking endpoints instead.
     */
    public function serviceRequestBids(Request $request, $serviceRequestId)
    {
        try {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint has been deprecated. The system has migrated from service requests to job bookings. Please use the job booking endpoints to view bids.',
                'data' => [],
                'migration_info' => [
                    'old_system' => 'service_requests + bids',
                    'new_system' => 'job_bookings + bids',
                    'recommended_endpoints' => [
                        'GET /api/customer/job-bookings' => 'List your job bookings with bids',
                        'GET /api/customer/job-bookings/{jobUuid}' => 'Get specific job booking with bids'
                    ]
                ]
            ], 410); // 410 Gone - indicates the resource is no longer available
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Update bid status (Task 12)
     */
    public function updateStatus(UpdateBidStatusRequest $request, Bid $bid)
    {
        try {
            // Check if user owns the job booking
            if ($bid->jobBooking->user_id !== Helpers::getCurrentUserId()) {
                throw new Exception('You can only update status for bids on your job bookings', 403);
            }

            $result = $this->repository->update($request->validated(), $bid->id);

            if ($result instanceof \App\Models\Service) {
                return response()->json([
                    'success' => true,
                    'data' => $result,
                    'message' => 'Bid accepted and service created successfully'
                ]);
            }

            $result->load(['jobBooking', 'jobBooking.user', 'provider']);

            return response()->json([
                'success' => true,
                'data' => new BidResource($result),
                'message' => 'Bid status updated successfully'
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Admin: Get all bids (Task 13)
     */
    public function adminBids(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !$user->hasRole(RoleEnum::SUPREME_ADMIN)) {
                throw new Exception('Only administrators can access this endpoint', 403);
            }



            $bids = Bid::whereNull('deleted_at')
                ->with(['jobBooking', 'jobBooking.user', 'provider']);

            // Apply filters
            if ($request->status) {
                $bids = $bids->where('status', $request->status);
            }

            if ($request->provider_id) {
                $bids = $bids->where('provider_id', $request->provider_id);
            }

            if ($request->job_booking_id) {
                $bids = $bids->where('job_booking_id', $request->job_booking_id);
            }

            if ($request->start_date && $request->end_date) {
                $bids = $bids->whereBetween('created_at', [$request->start_date, $request->end_date]);
            }

            if ($request->min_amount) {
                $bids = $bids->where('amount', '>=', $request->min_amount);
            }

            if ($request->max_amount) {
                $bids = $bids->where('amount', '<=', $request->max_amount);
            }

            // Apply sorting - support both old and new parameter names
            $sortField = $request->sortField ?? $request->field ?? 'created_at';
            $sortDirection = $request->sortDirection ?? $request->sort ?? 'desc';

            // Validate sort direction
            $sortDirection = in_array(strtolower($sortDirection), ['asc', 'desc']) ? $sortDirection : 'desc';

            // Map submittedAt to created_at for frontend compatibility
            if ($sortField === 'submittedAt') {
                $sortField = 'created_at';
            }

            // Define allowed sort fields
            $allowedSortFields = [
                'id', 'amount', 'status', 'created_at', 'updated_at',
                'estimated_completion_time', 'customer_name', 'provider_name'
            ];

            // Validate sort field - default to created_at if invalid
            if (!in_array($sortField, $allowedSortFields)) {
                $sortField = 'created_at';
            }

            // Handle relationship-based sorting
            if ($sortField === 'customer_name') {
                $bids = $bids->join('job_bookings', 'bids.job_booking_id', '=', 'job_bookings.id')
                            ->leftJoin('users', 'job_bookings.user_id', '=', 'users.id')
                            ->orderByRaw("COALESCE(users.name, job_bookings.contact_name) {$sortDirection}")
                            ->select('bids.*'); // Ensure we only select bid columns
            } elseif ($sortField === 'provider_name') {
                $bids = $bids->join('users as providers', 'bids.provider_id', '=', 'providers.id')
                            ->orderBy('providers.name', $sortDirection)
                            ->select('bids.*'); // Ensure we only select bid columns
            } else {
                // Standard field sorting
                $bids = $bids->orderBy($sortField, $sortDirection);
            }

            $bids = $bids->paginate($request->paginate ?? 15);

            return response()->json([
                'success' => true,
                'data' => BidResource::collection($bids->items()),
                'pagination' => [
                    'current_page' => $bids->currentPage(),
                    'last_page' => $bids->lastPage(),
                    'per_page' => $bids->perPage(),
                    'total' => $bids->total(),
                ]
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Admin: Get bidding statistics (Task 14)
     */
    public function adminBidStats(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !$user->hasRole(RoleEnum::SUPREME_ADMIN)) {
                throw new Exception('Only administrators can access this endpoint', 403);
            }

            // Base query with date filter if provided
            $baseQuery = Bid::whereNull('deleted_at');
            if ($request->start_date && $request->end_date) {
                $baseQuery->whereBetween('created_at', [$request->start_date, $request->end_date]);
            }

            // Get counts for each status using fresh queries
            $totalBids = (clone $baseQuery)->count();
            $acceptedBids = (clone $baseQuery)->where('status', 'accepted')->count();
            $rejectedBids = (clone $baseQuery)->where('status', 'rejected')->count();
            $pendingBids = (clone $baseQuery)->where('status', 'requested')->count();

            $conversionRate = $totalBids > 0 ? round(($acceptedBids / $totalBids) * 100, 2) : 0;

            // Get financial data using fresh queries
            $averageBidAmount = (clone $baseQuery)->avg('amount') ?? 0;
            $totalBidValue = (clone $baseQuery)->sum('amount') ?? 0;
            $acceptedBidValue = (clone $baseQuery)->where('status', 'accepted')->sum('amount') ?? 0;

            // Get top providers by bid count (with same date filter if applied)
            $topProvidersQuery = Bid::whereNull('deleted_at');
            if ($request->start_date && $request->end_date) {
                $topProvidersQuery->whereBetween('created_at', [$request->start_date, $request->end_date]);
            }

            $topProviders = $topProvidersQuery
                ->select('provider_id', DB::raw('COUNT(*) as bid_count'), DB::raw('AVG(amount) as avg_amount'))
                ->with('provider:id,name,email')
                ->groupBy('provider_id')
                ->orderByDesc('bid_count')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_bids' => $totalBids,
                        'accepted_bids' => $acceptedBids,
                        'rejected_bids' => $rejectedBids,
                        'pending_bids' => $pendingBids,
                        'conversion_rate' => $conversionRate . '%',
                    ],
                    'financial' => [
                        'average_bid_amount' => round($averageBidAmount, 2),
                        'total_bid_value' => round($totalBidValue, 2),
                        'accepted_bid_value' => round($acceptedBidValue, 2),
                    ],
                    'top_providers' => $topProviders->map(function ($provider) {
                        return [
                            'provider_id' => $provider->provider_id,
                            'provider_name' => $provider->provider->name,
                            'provider_email' => $provider->provider->email,
                            'bid_count' => $provider->bid_count,
                            'average_amount' => round($provider->avg_amount, 2),
                        ];
                    }),
                ]
            ]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

}