<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_uuid' => $this->job_uuid,
            'job_booking_id' => $this->job_booking_id,
            'bid_id' => $this->bid_id,
            'booking_id' => $this->booking_id,
            'customer_id' => $this->customer_id,
            'provider_id' => $this->provider_id,
            'status' => $this->status,
            'agreed_amount' => $this->agreed_amount,
            'estimated_completion_time' => $this->estimated_completion_time?->format('Y-m-d H:i:s'),
            'actual_start_time' => $this->actual_start_time?->format('Y-m-d H:i:s'),
            'actual_completion_time' => $this->actual_completion_time?->format('Y-m-d H:i:s'),
            'notes' => $this->notes,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Relationships
            'job_booking' => $this->whenLoaded('jobBooking', function () {
                return [
                    'id' => $this->jobBooking->id,
                    'job_uuid' => $this->jobBooking->job_uuid,
                    'project_code' => $this->jobBooking->project_code,
                    'service_category' => $this->jobBooking->service_category,
                    'description' => $this->jobBooking->description,
                    'status' => $this->jobBooking->status,
                    'schedule_date' => $this->jobBooking->schedule_date?->format('Y-m-d'),
                    'address' => $this->jobBooking->address,
                    'city' => $this->jobBooking->city,
                    'state' => $this->jobBooking->state,
                ];
            }),
            
            'provider' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'name' => $this->provider->name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),
            
            'customer' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->id,
                    'name' => $this->customer->name,
                    'email' => $this->customer->email,
                    'phone' => $this->customer->phone,
                ];
            }),
            
            'bid' => $this->whenLoaded('bid', function () {
                return [
                    'id' => $this->bid->id,
                    'amount' => $this->bid->amount,
                    'description' => $this->bid->description,
                    'estimated_completion_time' => $this->bid->estimated_completion_time?->format('Y-m-d H:i:s'),
                    'status' => $this->bid->status,
                ];
            }),
            
            'booking' => $this->whenLoaded('booking', function () {
                return [
                    'id' => $this->booking->id,
                    'status' => $this->booking->status,
                    'scheduled_date' => $this->booking->scheduled_date?->format('Y-m-d'),
                    'start_time' => $this->booking->start_time,
                    'end_time' => $this->booking->end_time,
                ];
            }),
        ];
    }
}
